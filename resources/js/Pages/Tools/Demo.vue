<script setup>
import { Head, <PERSON> } from '@inertiajs/vue3';
import PublicLayout from '@/Layouts/PublicLayout.vue';
import BookingBearWidget from '@/Components/BookingBearWidget.vue';

const props = defineProps({

});

const meta = {
    title: 'Booking Bear Demo',
    description: 'Demo of the Booking Bear widget - easily embeddable accommodation booking system'
};

const formatDate = (date) => {
    return new Date(date).toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'long',
        day: 'numeric'
    });
};
</script>

<template>
    <PublicLayout :title="meta.title">
        <Head>
            <title>{{ meta.title }}</title>
            <meta name="description" :content="meta.description" />
        </Head>

        <!-- Hero Section -->
        <section class="bg-gradient-to-br from-background to-primary/5 py-16 lg:py-24">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <div class="text-center">
                    <h1 class="text-4xl lg:text-6xl font-bold text-primary mb-6">
                        Booking Bear Demo
                    </h1>
                    <p class="text-xl text-gray-600 max-w-3xl mx-auto">
                        Below is a demo of what the Booking Bear widget will look like. This accommodation listing can easily be embedded on any site, simply by adding a few lines of code on your site where you want it to appear.
                    </p>
                </div>
            </div>
        </section>

        <!-- Widget Demo Section -->
        <section class="bg-white">
			<div class="container mx-auto max-w-7xl py-16 px-10">
				<BookingBearWidget />
			</div>
		</section>

    </PublicLayout>
</template>

<style scoped>

</style>

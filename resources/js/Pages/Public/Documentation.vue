<script setup>
import { ref } from 'vue';
import { Link } from '@inertiajs/vue3';
import SeoMeta from '@/Components/SeoMeta.vue';
import PublicLayout from '@/Layouts/PublicLayout.vue';

const pageSchema = {
	'@type': 'TechArticle',
	headline: 'BookingBear Documentation',
	description: 'Complete guide to using BookingBear for managing your accommodation bookings',
	publisher: {
		'@type': 'Organization',
		name: 'BookingBear',
		logo: {
			'@type': 'ImageObject',
			url: 'https://bookingbear.co.za/images/logo.svg'
		}
	},
	mainEntityOfPage: {
		'@type': 'WebPage',
		'@id': 'https://bookingbear.co.za/documentation'
	},
	keywords: 'booking management, accommodation booking, documentation, user guide, api reference',
	inLanguage: 'en-US',
	datePublished: '2025-05-20',
	dateModified: '2025-05-20',
	author: {
		'@type': 'Organization',
		name: 'BookingBear'
	}
};

const activeSection = ref('getting-started');

const sections = [
	{ id: 'getting-started', title: 'Getting Started' },
	{ id: 'widget-installation', title: 'Widget Installation' },
	{ id: 'customization', title: 'Widget Customization' },
	{ id: 'pricing-system', title: 'Pricing System' },
	{ id: 'unavailability-dates', title: 'Unavailability Dates' },
	{ id: 'api-reference', title: 'API Reference' }
];

const setActiveSection = (sectionId) => {
	activeSection.value = sectionId;
};
</script>

<template>
	<PublicLayout title="Documentation - BookingBear Guides & API Reference">
		<SeoMeta title="Documentation - BookingBear Guides & API Reference"
			description="Complete documentation for using BookingBear. Learn how to set up, customize, and manage your booking system with our comprehensive guides and API reference."
			:schema="pageSchema" />

		<div class="py-12 px-4">
			<div class="container mx-auto max-w-7xl">
				<h1 class="text-3xl md:text-4xl font-bold text-primary mb-8">Documentation</h1>

				<div class="flex flex-col lg:flex-row gap-8">
					<!-- Sidebar Navigation -->
					<div class="lg:w-1/4">
						<div class="bg-white rounded-lg shadow-sm p-6 sticky top-24">
							<h2 class="font-semibold text-lg mb-4 text-primary">Contents</h2>
							<ul class="space-y-2">
								<li v-for="section in sections" :key="section.id">
									<button @click="setActiveSection(section.id)" :class="[
										'w-full text-left px-3 py-2 rounded transition-colors',
										activeSection === section.id
											? 'bg-primary text-white font-medium'
											: 'text-gray-700 hover:bg-gray-100'
									]">
										{{ section.title }}
									</button>
								</li>
							</ul>
						</div>
					</div>

					<!-- Content Area -->
					<div class="lg:w-3/4">
						<div class="bg-white rounded-lg shadow-sm p-6 md:p-8">
							<!-- Getting Started Section -->
							<div v-show="activeSection === 'getting-started'">
								<h2 class="text-2xl font-bold text-primary mb-4">Getting Started with BookingBear</h2>
								<p class="mb-4">Welcome to BookingBear! This documentation will help you get started
									with our platform and make the most of its features.</p>

								<h3 class="text-xl font-semibold text-primary mt-8 mb-3">What is BookingBear?</h3>
								<p class="mb-4">BookingBear is a powerful platform designed to help accommodation owners
									manage bookings efficiently. With our easy-to-use widget, you can accept bookings
									directly on your website without any complex integrations.</p>

								<h3 class="text-xl font-semibold text-primary mt-8 mb-3">Key Features</h3>
								<ul class="list-disc pl-5 space-y-2 mb-6">
									<li>Simple widget embedding on any website</li>
									<li>Real-time availability management</li>
									<li>Flexible pricing options</li>
									<li>Booking request management</li>
									<li>Customizable appearance to match your brand</li>
								</ul>

								<h3 class="text-xl font-semibold text-primary mt-8 mb-3">Account Setup</h3>
								<p class="mb-4">To get started with BookingBear:</p>
								<ol class="list-decimal pl-5 space-y-2">
									<li>Create an account on the BookingBear platform</li>
									<li>Create an accommodation group if you have multiple accommodations</li>
									<li>Add your accommodations and set their details</li>
									<li>Configure your availability and pricing, either via group level pricing, or
										individual pricing</li>
									<li>Create a site embed, using your domain, to get your unique site ID. Your widget
										embed code will be shown to you once that has been done.</li>
								</ol>

								<h3 class="text-xl font-semibold text-primary mt-8 mb-3">Widget Demo</h3>
								<p>You can view an example of what the widget will look like here:
									<Link class="text-accent-primary hover:underline" href="/tools/demo">Widget Demo
									</Link>
								</p>
							</div>

							<!-- Widget Installation Section -->
							<div v-show="activeSection === 'widget-installation'">
								<h2 class="text-2xl font-bold text-primary mb-4">Widget Installation</h2>
								<p class="mb-4">Adding the BookingBear widget to your website is simple and requires
									just a few lines of code.</p>

								<h3 class="text-xl font-semibold text-primary mt-8 mb-3">Basic Installation</h3>
								<p class="mb-4">To embed the BookingBear widget on your website, add the following code
									to your HTML:</p>

								<div class="bg-gray-100 p-4 rounded-md mb-6">
									<pre class="text-sm overflow-x-auto"><code>&lt;availability-search
    site-id="SITE_ID_HERE"
&gt;
&lt;/availability-search&gt;

&lt;script src="https://widget.bookingbear.co.za/availability-search-v1.0.0.iife.js"&gt;&lt;/script&gt;</code></pre>
								</div>

								<p class="mb-4"><strong>Important:</strong> Replace <code
										class="bg-gray-100 px-1 rounded">SITE_ID_HERE</code> with your actual site ID
									provided in your BookingBear dashboard.</p>

								<h3 class="text-xl font-semibold text-primary mt-8 mb-3">Filtering by Accommodation
									Group</h3>
								<p class="mb-4">You can filter the displayed accommodations to show only those belonging
									to a specific group by adding the <code
										class="bg-gray-100 px-1 rounded">group-id</code> attribute:</p>

								<div class="bg-gray-100 p-4 rounded-md mb-6">
									<pre class="text-sm overflow-x-auto"><code>&lt;availability-search
    site-id="SITE_ID_HERE"
    group-id="GROUP_ID_HERE"
&gt;
&lt;/availability-search&gt;</code></pre>
								</div>

								<p class="mb-4">This is useful when you want to display only accommodations from a
									specific group on certain pages of your website. For example, you might have
									separate pages for different types of accommodations (e.g., cabins, villas,
									apartments) and want to show only the relevant options on each page.</p>

								<h3 class="text-xl font-semibold text-primary mt-8 mb-3">Placement Recommendations</h3>
								<p class="mb-4">For optimal user experience, we recommend placing the widget:</p>
								<ul class="list-disc pl-5 space-y-2 mb-6">
									<li>On your accommodation pages</li>
									<li>In a prominent position above the fold</li>
									<li>With sufficient space around it (at least 600px width)</li>
								</ul>

								<h3 class="text-xl font-semibold text-primary mt-8 mb-3">Security</h3>
								<p class="mb-4">Our widget uses domain-based authentication to ensure security:</p>
								<ul class="list-disc pl-5 space-y-2">
									<li>Your site ID is linked to your registered domain</li>
									<li>The widget automatically authenticates with our servers</li>
									<li>No API keys are exposed in your HTML</li>
									<li>All communication is secured with HTTPS</li>
								</ul>
							</div>

							<!-- Customization Section -->
							<div v-show="activeSection === 'customization'">
								<h2 class="text-2xl font-bold text-primary mb-4">Customization</h2>
								<p class="mb-4">The BookingBear widget can be customized to match your website's design
									using CSS variables.</p>

								<h3 class="text-xl font-semibold text-primary mt-8 mb-3">Styling with CSS Variables</h3>
								<p class="mb-4">Add the following CSS to your website to customize the widget's
									appearance:</p>

								<div class="bg-gray-100 p-4 rounded-md mb-6">
									<pre class="text-sm overflow-x-auto"><code>&lt;style&gt;
  .custom-theme {
    --bb-color-background: #E5F3FF;
    --bb-color-primary: #003B5C;
    --bb-color-accent-primary: #009B77;
    --bb-color-accent-secondary: #FFBF00;
    --bb-color-accent-cta: #F67891;
  }
&lt;/style&gt;</code></pre>
								</div>

								<h3 class="text-xl font-semibold text-primary mt-8 mb-3">Available CSS Variables</h3>
								<div class="overflow-x-auto mb-6">
									<table class="min-w-full border-collapse">
										<thead>
											<tr class="bg-gray-100">
												<th class="border border-gray-300 px-4 py-2 text-left">Variable</th>
												<th class="border border-gray-300 px-4 py-2 text-left">Description</th>
												<th class="border border-gray-300 px-4 py-2 text-left">Default Value
												</th>
											</tr>
										</thead>
										<tbody>
											<tr>
												<td class="border border-gray-300 px-4 py-2">
													<code>--bb-color-background</code></td>
												<td class="border border-gray-300 px-4 py-2">Widget background color
												</td>
												<td class="border border-gray-300 px-4 py-2">#FFFFFF</td>
											</tr>
											<tr>
												<td class="border border-gray-300 px-4 py-2">
													<code>--bb-color-primary</code></td>
												<td class="border border-gray-300 px-4 py-2">Primary text and border
													color</td>
												<td class="border border-gray-300 px-4 py-2">#003B5C</td>
											</tr>
											<tr>
												<td class="border border-gray-300 px-4 py-2">
													<code>--bb-color-accent-primary</code></td>
												<td class="border border-gray-300 px-4 py-2">Main accent color</td>
												<td class="border border-gray-300 px-4 py-2">#009B77</td>
											</tr>
											<tr>
												<td class="border border-gray-300 px-4 py-2">
													<code>--bb-color-accent-secondary</code></td>
												<td class="border border-gray-300 px-4 py-2">Secondary accent color</td>
												<td class="border border-gray-300 px-4 py-2">#FFBF00</td>
											</tr>
											<tr>
												<td class="border border-gray-300 px-4 py-2">
													<code>--bb-color-accent-cta</code></td>
												<td class="border border-gray-300 px-4 py-2">Call-to-action button color
												</td>
												<td class="border border-gray-300 px-4 py-2">#F67891</td>
											</tr>
										</tbody>
									</table>
								</div>

								<h3 class="text-xl font-semibold text-primary mt-8 mb-3">Accommodation Grouping Display
								</h3>
								<p class="mb-4">When displaying accommodations that belong to different groups, the
									widget automatically organizes them by group, showing the group name and description
									as headers. Accommodations that don't belong to any group are displayed in a
									separate "Other Accommodations" section at the bottom.</p>

								<p class="mb-4">This grouping helps your customers better understand the different types
									or categories of accommodations you offer, making it easier for them to find what
									they're looking for.</p>

								<h3 class="text-xl font-semibold text-primary mt-8 mb-3">Advanced Customization</h3>
								<p class="mb-4">For more advanced customization options, please contact our support
									team.</p>
							</div>

							<!-- Pricing System Section -->
							<div v-show="activeSection === 'pricing-system'">
								<h2 class="text-2xl font-bold text-primary mb-4">Pricing System</h2>
								<p class="mb-4">BookingBear offers a flexible and powerful pricing system that allows
									you to set prices at both individual accommodation and group levels, with various
									pricing rules to handle different scenarios.</p>

								<h3 class="text-xl font-semibold text-primary mt-8 mb-3">Pricing Overview</h3>
								<p class="mb-4">The pricing system in BookingBear is designed to be flexible and
									accommodate various pricing strategies:</p>
								<ul class="list-disc pl-5 space-y-2 mb-6">
									<li>Set prices for individual accommodations or for entire accommodation groups</li>
									<li>Define different pricing for specific date ranges (e.g., peak seasons)</li>
									<li>Set different rates for specific days of the week (e.g., weekend rates)</li>
									<li>Adjust pricing based on occupancy (e.g., additional person charges)</li>
								</ul>

								<h3 class="text-xl font-semibold text-primary mt-8 mb-3">Individual vs. Group Pricing
								</h3>
								<p class="mb-4">BookingBear allows you to set prices at two levels:</p>

								<div class="grid md:grid-cols-2 gap-6 mb-6">
									<div class="bg-gray-50 p-5 rounded-lg border border-gray-200">
										<h4 class="font-semibold text-lg mb-2 text-primary">Individual Accommodation
											Pricing</h4>
										<p>Prices set directly on a specific accommodation. These take precedence over
											any group pricing.</p>
										<ul class="list-disc pl-5 mt-3 space-y-1">
											<li>Specific to a single accommodation</li>
											<li>Overrides any group pricing</li>
											<li>Ideal for unique or premium accommodations</li>
										</ul>
									</div>

									<div class="bg-gray-50 p-5 rounded-lg border border-gray-200">
										<h4 class="font-semibold text-lg mb-2 text-primary">Group-Based Pricing</h4>
										<p>Prices set at the accommodation group level. These apply to all
											accommodations in the group that don't have individual pricing.</p>
										<ul class="list-disc pl-5 mt-3 space-y-1">
											<li>Applied to all accommodations in a group</li>
											<li>Serves as a fallback when individual pricing isn't set</li>
											<li>Efficient for managing similar accommodations</li>
										</ul>
									</div>
								</div>

								<h3 class="text-xl font-semibold text-primary mt-8 mb-3">Best Practices</h3>
								<ul class="list-disc pl-5 space-y-2 mb-6">
									<li><strong>Always set a default price</strong> for either individual accommodations
										or groups</li>
									<li>Use <strong>group pricing</strong> for accommodations with similar
										characteristics and rates</li>
									<li>Use <strong>individual pricing</strong> for accommodations that need unique
										pricing</li>
									<li>Set <strong>date range pricing</strong> well in advance for seasonal periods
									</li>
									<li>Remember that <strong>prices are stored in cents</strong> but displayed in rands
										(ZAR)</li>
									<li>Accommodations must have pricing (either individual or group) before they can be
										published</li>
								</ul>
							</div>

							<!-- Unavailability Dates Section -->
							<div v-show="activeSection === 'unavailability-dates'">
								<h2 class="text-2xl font-bold text-primary mb-4">Accommodation Unavailability Dates</h2>
								<p class="mb-4">BookingBear allows you to mark specific dates or recurring days as
									unavailable for booking, giving you full control over your accommodation's
									availability calendar.</p>

								<h3 class="text-xl font-semibold text-primary mt-8 mb-3">Understanding Unavailability
									Dates</h3>
								<p class="mb-4">Unavailability dates let you block specific periods when your
									accommodation cannot be booked, such as:</p>
								<ul class="list-disc pl-5 space-y-2 mb-6">
									<li>Personal use of the accommodation</li>
									<li>Maintenance or renovation periods</li>
									<li>Seasonal closures</li>
									<li>Regular weekly closures (e.g., always closed on Mondays)</li>
								</ul>

								<h3 class="text-xl font-semibold text-primary mt-8 mb-3">Types of Unavailability Periods
								</h3>
								<div class="grid md:grid-cols-2 gap-6 mb-6">
									<div class="bg-gray-50 p-5 rounded-lg border border-gray-200">
										<h4 class="font-semibold text-lg mb-2 text-primary">Date Range Periods</h4>
										<p>Block specific date ranges when your accommodation is unavailable.</p>
										<ul class="list-disc pl-5 mt-3 space-y-1">
											<li>Specify a start and end date</li>
											<li>Ideal for one-time events or seasonal closures</li>
											<li>Can include an optional reason for the block</li>
										</ul>
									</div>

									<div class="bg-gray-50 p-5 rounded-lg border border-gray-200">
										<h4 class="font-semibold text-lg mb-2 text-primary">Recurring Days</h4>
										<p>Block specific days of the week that repeat indefinitely.</p>
										<ul class="list-disc pl-5 mt-3 space-y-1">
											<li>Select one or more days of the week</li>
											<li>Perfect for regular weekly closures</li>
											<li>Applies to all future bookings</li>
										</ul>
									</div>
								</div>

								<h3 class="text-xl font-semibold text-primary mt-8 mb-3">Best Practices</h3>
								<ul class="list-disc pl-5 space-y-2 mb-6">
									<li>Use <strong>date range periods</strong> for specific, one-time blocks</li>
									<li>Use <strong>recurring days</strong> for regular weekly patterns</li>
									<li>Add a <strong>reason</strong> for unavailability to help you remember why dates
										were blocked</li>
									<li>Regularly review your unavailability calendar to ensure it's up to date</li>
									<li>Consider setting unavailable periods well in advance for known closures</li>
									<li>Remember that existing bookings automatically block those dates from being
										double-booked</li>
								</ul>
							</div>

							<!-- API Reference Section -->
							<div v-show="activeSection === 'api-reference'">
								<h2 class="text-2xl font-bold text-primary mb-4">API Reference</h2>
								<p class="mb-4">BookingBear provides a comprehensive API for developers who want to
									integrate our services more deeply into their applications.</p>

								<h3 class="text-xl font-semibold text-primary mt-8 mb-3">Authentication</h3>
								<p class="mb-4">All API requests require authentication using a Bearer token. To obtain
									a token:</p>
								<ol class="list-decimal pl-5 space-y-2 mb-6">
									<li>Generate an API key in your BookingBear dashboard</li>
									<li>Use the key to request a token from the authentication endpoint</li>
									<li>Include the token in the Authorization header of all API requests</li>
								</ol>

								<h3 class="text-xl font-semibold text-primary mt-8 mb-3">Endpoints</h3>
								<p class="mb-4">Here are the main endpoints available in our API:</p>

								<div class="overflow-x-auto mb-6">
									<table class="min-w-full border-collapse">
										<thead>
											<tr class="bg-gray-100">
												<th class="border border-gray-300 px-4 py-2 text-left">Endpoint</th>
												<th class="border border-gray-300 px-4 py-2 text-left">Method</th>
												<th class="border border-gray-300 px-4 py-2 text-left">Description</th>
											</tr>
										</thead>
										<tbody>
											<tr>
												<td class="border border-gray-300 px-4 py-2">
													<code>/api/widget-auth</code></td>
												<td class="border border-gray-300 px-4 py-2">POST</td>
												<td class="border border-gray-300 px-4 py-2">Authenticate widget and get
													temporary token</td>
											</tr>
											<tr>
												<td class="border border-gray-300 px-4 py-2">
													<code>/api/accommodations</code></td>
												<td class="border border-gray-300 px-4 py-2">GET</td>
												<td class="border border-gray-300 px-4 py-2">List all accommodations
												</td>
											</tr>
											<tr>
												<td class="border border-gray-300 px-4 py-2">
													<code>/api/availability</code></td>
												<td class="border border-gray-300 px-4 py-2">GET</td>
												<td class="border border-gray-300 px-4 py-2">Check availability for
													dates</td>
											</tr>
											<tr>
												<td class="border border-gray-300 px-4 py-2"><code>/api/bookings</code>
												</td>
												<td class="border border-gray-300 px-4 py-2">POST</td>
												<td class="border border-gray-300 px-4 py-2">Create a new booking</td>
											</tr>
										</tbody>
									</table>
								</div>

								<!--<p class="mb-4">For detailed API documentation, including request and response formats, please visit our <a href="/docs/api" class="text-accent-primary hover:underline">API Documentation Portal</a>.</p>-->
								<p class="mb-4">Details full API documentation COMING SOON!</p>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
	</PublicLayout>
</template>

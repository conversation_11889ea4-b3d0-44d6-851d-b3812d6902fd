<template>
  <availability-search :class="themeClass" :site-id="siteId"></availability-search>
</template>

<script setup>
import { onMounted } from 'vue'

const props = defineProps({
  siteId: {
    type: String,
    default: 'xyZYMpZQWL3n'
  },
  themeClass: {
    type: String,
    default: 'custom-theme'
  }
})

onMounted(() => {
  if (typeof window !== 'undefined') {
    if (!document.getElementById('bookingbear-widget-script')) {
      const script = document.createElement('script')
      script.src = 'https://widget.bookingbear.co.za/availability-search-v1.0.0.iife.js'
      script.async = true
      script.id = 'bookingbear-widget-script'
      document.head.appendChild(script)
    }
  }
})
</script>

<style scoped>
/* Minimal wrapper styling - leave main styling to consumer */
</style>
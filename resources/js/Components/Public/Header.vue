<template>
	<header class="w-full bg-white shadow-sm sticky top-0 z-50">
		<div v-if="!$page.props.auth.user" class="w-full py-2 bg-primary">
			<div class="container mx-auto px-4 py-4 text-center text-white font-semibold">
				<strong>LIMITED OFFER!</strong> During our beta period we're offering the first 10 users a 2 Month FREE Pro subscription. <Link :href="route('register')" class="text-accent-cta hover:bg-[#e56a82] underline transition-all duration-300">Get Started For Free</Link>. 
			</div>
		</div>
		<div class="container mx-auto px-4 py-4 flex items-center justify-between">
			<div class="flex items-center">
				<Link :href="route('welcome')" class="text-primary font-bold text-2xl flex items-center gap-3">
					<ApplicationMark class="block w-12" :show-text="false" :beta="true" />
					Booking Bear
				</Link>
			</div>
			<nav class="hidden md:flex items-center space-x-8">
				<Link :href="'/#features'" class="text-primary hover:text-accent-primary font-medium">
					Features
				</Link>
				<Link :href="'/#pricing'" class="text-primary hover:text-accent-primary font-medium">
					Pricing
				</Link>
				<Link :href="route('blog.index')" class="text-primary hover:text-accent-primary font-medium">
					Blog
				</Link>

				<!-- Tools Dropdown -->
				<div class="relative group">
					<button class="flex items-center text-primary hover:text-accent-primary font-medium focus:outline-none">
						<span>Tools</span>
						<svg class="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
							<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
						</svg>
					</button>
					<div class="absolute left-0 mt-2 w-48 bg-white dark:bg-slate-800 rounded-md shadow-lg py-1 z-50 opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200 transform translate-y-1 group-hover:translate-y-0">
						<Link :href="route('tools.pricing-calculator')" class="block px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-slate-700">
							Pricing Calculator
						</Link>
						<Link :href="route('tools.demo')" class="block px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-slate-700">
							Widget Demo
						</Link>
						<!-- Add more tools here as they become available -->
					</div>
				</div>

				<FeedbackFish projectId="ccbf1443161c97" />
				<a data-feedback-fish href="#" class="text-primary hover:text-accent-primary font-medium">
					Feedback
				</a>

				<Link
					v-if="$page.props.auth.user"
					:href="route('dashboard')"
					class="bg-accent-cta hover:bg-[#e56a82] text-white px-4 py-2 rounded-md font-medium"
				>
					Dashboard
				</Link>

				<template v-else>
					<Link
						:href="route('login')"
						class="text-primary hover:text-accent-primary font-medium"
					>
						Log in
					</Link>

					<Link
						:href="route('register')"
						class="bg-accent-cta hover:bg-[#e56a82] text-white px-4 py-2 rounded-md font-medium"
					>
						Sign Up Free
					</Link>
				</template>

			</nav>
			<div class="md:hidden">
				<button @click="isMenuOpen = !isMenuOpen" class="text-primary focus:outline-none">
					<XIcon v-if="isMenuOpen" :size="24" />
					<MenuIcon v-else :size="24" />
				</button>
			</div>
		</div>
		<div v-if="isMenuOpen" class="md:hidden bg-white px-4 py-2 shadow-md">
			<div class="flex flex-col space-y-3 pb-3">
				<Link :href="'/#features'" class="text-primary hover:text-accent-primary font-medium">
					Features
				</Link>
				<Link :href="'/#pricing'" class="text-primary hover:text-accent-primary font-medium">
					Pricing
				</Link>
				<Link :href="route('blog.index')" class="text-primary hover:text-accent-primary font-medium">
					Blog
				</Link>
				<Link :href="route('tools.pricing-calculator')" class="text-primary hover:text-accent-primary font-medium">
					Pricing Calculator
				</Link>
				<!--<Link :href="'/#testimonials'" class="text-primary hover:text-accent-primary font-medium">
					Testimonials
				</Link>-->
				<Link
					v-if="$page.props.auth.user"
					:href="route('dashboard')"
					class="bg-accent-cta hover:bg-[#e56a82] text-white px-4 py-2 rounded-md font-medium"
				>
					Dashboard
				</Link>

				<template v-else>
					<Link
						:href="route('login')"
						class="text-primary hover:text-accent-primary font-medium"
					>
						Log in
					</Link>

					<Link
						:href="route('register')"
						class="bg-accent-cta hover:bg-[#e56a82] text-white px-4 py-2 rounded-md font-medium text-center"
					>
						Sign Up Free
					</Link>
				</template>
			</div>
		</div>
	</header>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { Menu as MenuIcon, X as XIcon } from 'lucide-vue-next'
import { Link } from '@inertiajs/vue3';
import ApplicationMark from '@/Components/ApplicationMark.vue';
import { FeedbackFish } from "@feedback-fish/vue";

const isMenuOpen = ref(false)
</script>

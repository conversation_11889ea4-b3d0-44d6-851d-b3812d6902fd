<?php

namespace App\Http\Controllers\API;

use App\Http\Controllers\Controller;
use App\Models\Accommodation;
use App\Services\AvailabilityChecker;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use OpenApi\Annotations as OA;

/**
 * @OA\Tag(
 *     name="Accommodations",
 *     description="API endpoints for managing accommodations"
 * )
 */
class AccommodationApiController extends Controller
{
    protected $availabilityChecker;

    public function __construct(AvailabilityChecker $availabilityChecker)
    {
        $this->availabilityChecker = $availabilityChecker;
    }

    /**
     * Get a list of accommodations for the authenticated user.
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     *
     * @OA\Get(
     *     path="/api/accommodations",
     *     summary="Get list of accommodations",
     *     description="Returns a list of accommodations belonging to the authenticated user, with optional filtering by group",
     *     operationId="getAccommodations",
     *     tags={"Accommodations"},
     *     security={{"sanctum": {}}},
     *     @OA\Parameter(
     *         name="group_id",
     *         in="query",
     *         description="Filter by accommodation group ID",
     *         required=false,
     *         @OA\Schema(type="integer")
     *     ),
     *     @OA\Parameter(
     *         name="published",
     *         in="query",
     *         description="Filter by published status (1 for published, 0 for unpublished)",
     *         required=false,
     *         @OA\Schema(type="integer", enum={0, 1})
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="List of accommodations",
     *         @OA\JsonContent(
     *             type="object",
     *             @OA\Property(property="data", type="array", @OA\Items(
     *                 @OA\Property(property="id", type="integer", example=1),
     *                 @OA\Property(property="name", type="string", example="Beach House"),
     *                 @OA\Property(property="description", type="string", example="Beautiful beach house with ocean view"),
     *                 @OA\Property(property="address", type="string", example="123 Beach Road"),
     *                 @OA\Property(property="city", type="string", example="Cape Town"),
     *                 @OA\Property(property="province", type="string", example="Western Cape"),
     *                 @OA\Property(property="post_code", type="string", example="8001"),
     *                 @OA\Property(property="country", type="string", example="South Africa"),
     *                 @OA\Property(property="min_occupancy", type="integer", example=2),
     *                 @OA\Property(property="max_occupancy", type="integer", example=4),
     *                 @OA\Property(property="minimum_booking_notice", type="integer", example=1),
     *                 @OA\Property(property="published", type="boolean", example=true),
     *                 @OA\Property(property="default_price", type="string", example="100.00"),
     *                 @OA\Property(property="gallery_images", type="array", @OA\Items(
     *                     @OA\Property(property="id", type="integer", example=1),
     *                     @OA\Property(property="name", type="string", example="beach-house-1"),
     *                     @OA\Property(property="file_name", type="string", example="beach-house-1.jpg"),
     *                     @OA\Property(property="mime_type", type="string", example="image/jpeg"),
     *                     @OA\Property(property="original_url", type="string", example="https://example.com/storage/1/beach-house-1.jpg"),
     *                     @OA\Property(property="thumbnail", type="string", example="https://example.com/storage/1/conversions/beach-house-1-thumb.jpg"),
     *                     @OA\Property(property="medium", type="string", example="https://example.com/storage/1/conversions/beach-house-1-medium.jpg"),
     *                     @OA\Property(property="large", type="string", example="https://example.com/storage/1/conversions/beach-house-1-large.jpg")
     *                 )),
     *                 @OA\Property(property="group", type="object", nullable=true,
     *                     @OA\Property(property="id", type="integer", example=1),
     *                     @OA\Property(property="name", type="string", example="Beach Properties"),
     *                     @OA\Property(property="description", type="string", example="Properties near the beach")
     *                 )
     *             )),
     *             @OA\Property(property="meta", type="object",
     *                 @OA\Property(property="total", type="integer", example=10),
     *                 @OA\Property(property="per_page", type="integer", example=15),
     *                 @OA\Property(property="current_page", type="integer", example=1),
     *                 @OA\Property(property="last_page", type="integer", example=1)
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=401,
     *         description="Unauthenticated",
     *         @OA\JsonContent(
     *             @OA\Property(property="message", type="string", example="Unauthenticated.")
     *         )
     *     )
     * )
     */
    public function index(Request $request)
    {
        // For authenticated user requests
		$user = Auth::user();
		$teamIds = $user->allTeams()->pluck('id')->toArray();

		$query = Accommodation::where(function ($query) use ($user, $teamIds) {
				$query->where('user_id', $user->id);

				if (!empty($teamIds)) {
					$query->orWhereIn('team_id', $teamIds);
				}
			})
			->with(['group']);

        // Filter by published status if provided
        if ($request->has('published')) {
            $query->where('published', filter_var($request->published, FILTER_VALIDATE_BOOLEAN));
        }

        // Filter by group if provided
        if ($request->has('group_id')) {
            $query->where('accommodation_group_id', $request->group_id);
        }

        $accommodations = $query->paginate(15);

        return response()->json($accommodations);
    }

    /**
     * Get a specific accommodation by ID.
     *
     * @param int $id
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     *
     * @OA\Get(
     *     path="/api/accommodations/{id}",
     *     summary="Get accommodation details",
     *     description="Returns details of a specific accommodation",
     *     operationId="getAccommodation",
     *     tags={"Accommodations"},
     *     security={{"sanctum": {}}},
     *     @OA\Parameter(
     *         name="id",
     *         in="path",
     *         description="Accommodation ID",
     *         required=true,
     *         @OA\Schema(type="integer")
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Accommodation details",
     *         @OA\JsonContent(
     *             @OA\Property(property="id", type="integer", example=1),
     *             @OA\Property(property="name", type="string", example="Beach House"),
     *             @OA\Property(property="description", type="string", example="Beautiful beach house with ocean view"),
     *             @OA\Property(property="address", type="string", example="123 Beach Road"),
     *             @OA\Property(property="city", type="string", example="Cape Town"),
     *             @OA\Property(property="province", type="string", example="Western Cape"),
     *             @OA\Property(property="post_code", type="string", example="8001"),
     *             @OA\Property(property="country", type="string", example="South Africa"),
     *             @OA\Property(property="min_occupancy", type="integer", example=2),
     *             @OA\Property(property="max_occupancy", type="integer", example=4),
     *             @OA\Property(property="minimum_booking_notice", type="integer", example=1),
     *             @OA\Property(property="published", type="boolean", example=true),
     *             @OA\Property(property="default_price", type="string", example="100.00"),
     *             @OA\Property(property="gallery_images", type="array", @OA\Items(
     *                 @OA\Property(property="id", type="integer", example=1),
     *                 @OA\Property(property="name", type="string", example="beach-house-1"),
     *                 @OA\Property(property="file_name", type="string", example="beach-house-1.jpg"),
     *                 @OA\Property(property="mime_type", type="string", example="image/jpeg"),
     *                 @OA\Property(property="original_url", type="string", example="https://example.com/storage/1/beach-house-1.jpg"),
     *                 @OA\Property(property="thumbnail", type="string", example="https://example.com/storage/1/conversions/beach-house-1-thumb.jpg"),
     *                 @OA\Property(property="medium", type="string", example="https://example.com/storage/1/conversions/beach-house-1-medium.jpg"),
     *                 @OA\Property(property="large", type="string", example="https://example.com/storage/1/conversions/beach-house-1-large.jpg")
     *             )),
     *             @OA\Property(property="group", type="object", nullable=true,
     *                 @OA\Property(property="id", type="integer", example=1),
     *                 @OA\Property(property="name", type="string", example="Beach Properties"),
     *                 @OA\Property(property="description", type="string", example="Properties near the beach")
     *             ),
     *             @OA\Property(property="prices", type="array", @OA\Items(
     *                 @OA\Property(property="id", type="integer", example=1),
     *                 @OA\Property(property="price", type="number", format="float", example=100.00),
     *                 @OA\Property(property="additional_person_price", type="number", format="float", example=25.00),
     *                 @OA\Property(property="type", type="string", example="default"),
     *                 @OA\Property(property="start_date", type="string", format="date", nullable=true),
     *                 @OA\Property(property="end_date", type="string", format="date", nullable=true),
     *                 @OA\Property(property="day_of_week", type="integer", nullable=true, example=0),
     *                 @OA\Property(property="priority", type="integer", example=1)
     *             ))
     *         )
     *     ),
     *     @OA\Response(
     *         response=404,
     *         description="Accommodation not found",
     *         @OA\JsonContent(
     *             @OA\Property(property="message", type="string", example="Accommodation not found or you do not have permission to access it")
     *         )
     *     ),
     *     @OA\Response(
     *         response=401,
     *         description="Unauthenticated",
     *         @OA\JsonContent(
     *             @OA\Property(property="message", type="string", example="Unauthenticated.")
     *         )
     *     )
     * )
     */
    public function show($id, Request $request)
    {
        // Check if this is a widget request (has authenticated_site attribute)
        $isWidgetRequest = $request->attributes->has('authenticated_site');

        if ($isWidgetRequest) {
            // For widget requests, we only show published accommodations
            $accommodation = Accommodation::where('id', $id)
                ->where('published', true)
                ->with(['group', 'prices'])
                ->first();
        } else {
            // For authenticated user requests
            $user = Auth::user();
            $teamIds = $user->allTeams()->pluck('id')->toArray();

            $accommodation = Accommodation::where('id', $id)
                ->where(function ($query) use ($user, $teamIds) {
                    $query->where('user_id', $user->id);

                    if (!empty($teamIds)) {
                        $query->orWhereIn('team_id', $teamIds);
                    }
                })
                ->with(['media', 'group', 'prices'])
                ->first();
        }

        if (!$accommodation) {
            return response()->json(['message' => 'Accommodation not found or you do not have permission to access it'], 404);
        }

        return response()->json(['data' => $accommodation]);
    }

    /**
     * Check the availability of the specified accommodation for given dates.
     *
     * @param string $id The ID of the accommodation to check
     * @param Request $request The request containing start_date, end_date, and number_of_persons
     * @return \Illuminate\Http\JsonResponse
     *
     * @OA\Post(
     *     path="/api/accommodations/{id}/check-availability",
     *     summary="Check accommodation availability for specific dates",
     *     description="Returns availability status, pricing information, and any conflicts for the specified dates",
     *     operationId="checkAccommodationAvailabilityPost",
     *     tags={"Accommodations"},
     *     security={{"sanctum": {}}},
     *     @OA\Parameter(
     *         name="id",
     *         in="path",
     *         required=true,
     *         description="Accommodation ID",
     *         @OA\Schema(type="string")
     *     ),
     *     @OA\RequestBody(
     *         required=true,
     *         description="Availability check parameters",
     *         @OA\JsonContent(
     *             required={"start_date", "end_date"},
     *             @OA\Property(property="start_date", type="string", format="date", example="2023-06-01", description="Start date (format: Y-m-d)"),
     *             @OA\Property(property="end_date", type="string", format="date", example="2023-06-05", description="End date (format: Y-m-d)"),
     *             @OA\Property(property="number_of_persons", type="integer", example=2, description="Number of persons (defaults to minimum occupancy)")
     *         )
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Availability information",
     *         @OA\JsonContent(
     *             @OA\Property(property="available", type="boolean", example=true),
     *             @OA\Property(property="message", type="string", example="Accommodation is available for the specified dates"),
     *             @OA\Property(property="total_price", type="number", format="float", example=1250.00),
     *             @OA\Property(
     *                 property="pricing",
     *                 type="object",
     *                 @OA\Property(property="daily_prices", type="object", example={"2023-06-01": 250, "2023-06-02": 250, "2023-06-03": 300, "2023-06-04": 300, "2023-06-05": 150}),
     *                 @OA\Property(property="total_price", type="number", format="float", example=1250.00),
     *                 @OA\Property(property="currency", type="string", example="ZAR"),
     *                 @OA\Property(property="occupancy", type="integer", example=2),
     *                 @OA\Property(property="min_occupancy", type="integer", example=1),
     *                 @OA\Property(property="max_occupancy", type="integer", example=4)
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=400,
     *         description="Invalid input",
     *         @OA\JsonContent(
     *             @OA\Property(property="start_date", type="array", @OA\Items(type="string"), example={"The start date field is required."}),
     *             @OA\Property(property="end_date", type="array", @OA\Items(type="string"), example={"The end date must be a date after start date."})
     *         )
     *     ),
     *     @OA\Response(
     *         response=404,
     *         description="Accommodation not found",
     *         @OA\JsonContent(
     *             @OA\Property(property="message", type="string", example="Accommodation not found")
     *         )
     *     )
     * )
     */
    public function checkAvailability(string $id, Request $request)
    {
        // Check if this is a widget request (has authenticated_site attribute)
        $isWidgetRequest = $request->attributes->has('authenticated_site');

        if ($isWidgetRequest) {
            // For widget requests, we only check published accommodations
            $accommodation = Accommodation::published()
                ->where('id', $id)
                ->first();
        } else {
            // For authenticated user requests
            $user = Auth::user();
            $teamIds = $user->allTeams()->pluck('id')->toArray();

            // Find accommodation and ensure it belongs to the authenticated user or their team and is published
            $accommodation = Accommodation::published()
                ->where('id', $id)
                ->where(function ($query) use ($user, $teamIds) {
                    $query->where('user_id', $user->id);

                    if (!empty($teamIds)) {
                        $query->orWhereIn('team_id', $teamIds);
                    }
                })
                ->first();
        }

        if (!$accommodation) {
            return response()->json(['message' => 'Accommodation not found or you do not have permission to access it'], 404);
        }

        $validator = \Illuminate\Support\Facades\Validator::make($request->all(), [
            'start_date' => 'required|date',
            'end_date' => 'required|date|after_or_equal:start_date',
            'occupancy' => 'sometimes|integer|min:1',
            'number_of_persons' => 'sometimes|integer|min:1',
        ]);

        if ($validator->fails()) {
            return response()->json($validator->errors(), 400);
        }

        $start_date = $request->input('start_date');
        $end_date = $request->input('end_date');

        // Get number of persons from either 'number_of_persons' or 'occupancy' parameter
        // This allows backward compatibility with the old 'occupancy' parameter
        $requestedOccupancy = $request->input('number_of_persons', $request->input('occupancy'));

        // Log the search attempt
        $searchData = [
            'accommodation_id' => $accommodation->id,
            'user_id' => $accommodation->user_id,
            'start_date' => $start_date,
            'end_date' => $end_date,
            'requested_occupancy' => $requestedOccupancy,
            'searched_at' => now(),
        ];

        // Check minimum booking notice
        if ($accommodation->minimum_booking_notice > 0) {
            $today = now()->startOfDay();
            $bookingStartDate = \Carbon\Carbon::parse($start_date)->startOfDay();
            $daysUntilBooking = $today->diffInDays($bookingStartDate);

            if ($daysUntilBooking < $accommodation->minimum_booking_notice) {
                \App\Models\AccommodationSearch::create([
                    ...$searchData,
                    'was_available' => false,
                    'unavailability_reason' => 'minimum_notice'
                ]);

                return response()->json([
                    'available' => false,
                    'message' => "Booking must be made at least {$accommodation->minimum_booking_notice} days in advance",
                    'reason' => 'minimum_notice',
                    'details' => [
                        'minimum_booking_notice' => $accommodation->minimum_booking_notice,
                        'days_until_booking' => $daysUntilBooking
                    ]
                ]);
            }
        }

        // Check minimum stay requirement
        $nights = \Carbon\Carbon::parse($start_date)->diffInDays(\Carbon\Carbon::parse($end_date));
        if ($nights < $accommodation->minimum_stay) {
            \App\Models\AccommodationSearch::create([
                ...$searchData,
                'was_available' => false,
                'unavailability_reason' => 'minimum_stay'
            ]);

            return response()->json([
                'available' => false,
                'message' => "Minimum stay is {$accommodation->minimum_stay} nights",
                'reason' => 'minimum_stay',
                'details' => [
                    'minimum_stay' => $accommodation->minimum_stay,
                    'nights' => $nights
                ]
            ]);
        }

        // Check if there are any bookings
        $bookings = \App\Models\Booking::where('accommodation_id', $id)
            ->where(function ($query) use ($start_date, $end_date) {
                $query->whereBetween('start_date', [$start_date, $end_date])
                    ->orWhereBetween('end_date', [$start_date, $end_date])
                    ->orWhere(function ($query) use ($start_date, $end_date) {
                        $query->where('start_date', '<=', $start_date)
                            ->where('end_date', '>=', $end_date);
                    });
            })
            ->count();

        if ($bookings > 0) {
            \App\Models\AccommodationSearch::create([
                ...$searchData,
                'was_available' => false,
                'unavailability_reason' => 'existing_booking'
            ]);

            return response()->json([
                'available' => false,
                'message' => 'Accommodation is not available for the specified dates',
                'reason' => 'existing_booking'
            ]);
        }

        // Check for unavailable periods using AvailabilityChecker
        if ($this->availabilityChecker->hasAnyConflict($accommodation, $start_date, $end_date)) {
            \App\Models\AccommodationSearch::create([
                ...$searchData,
                'was_available' => false,
                'unavailability_reason' => 'blocked_period'
            ]);

            return response()->json([
                'available' => false,
                'message' => 'Accommodation is not available for the specified dates',
                'reason' => 'blocked_period'
            ]);
        }

        // Get occupancy from request or use minimum occupancy
        $occupancy = $requestedOccupancy ?? $accommodation->min_occupancy;

        // Validate occupancy against accommodation limits
        if ($occupancy < $accommodation->min_occupancy) {
            return response()->json([
                'available' => false,
                'message' => 'Requested occupancy is below minimum required',
                'reason' => 'occupancy_limit',
                'details' => [
                    'requested_occupancy' => $occupancy,
                    'min_occupancy' => $accommodation->min_occupancy,
                    'max_occupancy' => $accommodation->max_occupancy
                ]
            ], 400);
        }

        if ($occupancy > $accommodation->max_occupancy) {
            return response()->json([
                'available' => false,
                'message' => 'Requested occupancy exceeds maximum allowed',
                'reason' => 'occupancy_limit',
                'details' => [
                    'requested_occupancy' => $occupancy,
                    'min_occupancy' => $accommodation->min_occupancy,
                    'max_occupancy' => $accommodation->max_occupancy
                ]
            ], 400);
        }

        // Calculate daily prices and total based on nights, not days
        $currentDate = new \DateTime($start_date);
        $endDateTime = new \DateTime($end_date);
        $dailyPrices = [];
        $totalPrice = 0;

        // Only iterate until the day before the end date (to count nights, not days)
        while ($currentDate < $endDateTime) {
            $dateStr = $currentDate->format('Y-m-d');
            $price = $accommodation->getPriceForDate($dateStr, $occupancy);
            $dailyPrices[$dateStr] = $price;
            $totalPrice += $price;
            $currentDate->modify('+1 day');
        }

        // Log successful search with price
        \App\Models\AccommodationSearch::create([
            ...$searchData,
            'was_available' => true,
            'quoted_price' => $totalPrice
        ]);

        return response()->json([
            'available' => true,
            'message' => 'Accommodation is available for the specified dates',
            'total_price' => $totalPrice,
            'pricing' => [
                'daily_prices' => $dailyPrices,
                'total_price' => $totalPrice,
                'currency' => 'R',
                'occupancy' => $occupancy,
                'min_occupancy' => $accommodation->min_occupancy,
                'max_occupancy' => $accommodation->max_occupancy
            ]
        ]);
    }

    /**
     * Check the availability of the specified accommodation for given dates (GET method for backward compatibility).
     *
     * @param string $id The ID of the accommodation to check
     * @param Request $request The request containing start_date, end_date, and occupancy
     * @return \Illuminate\Http\JsonResponse
     *
     * @OA\Get(
     *     path="/api/accommodations/{id}/check-availability",
     *     summary="[DEPRECATED] Check accommodation availability for specific dates",
     *     description="Returns availability status, pricing information, and any conflicts for the specified dates. This GET endpoint is maintained for backward compatibility and will be removed in a future version. Please use the POST endpoint instead.",
     *     operationId="checkAccommodationAvailabilityGet",
     *     tags={"Accommodations"},
     *     security={{"sanctum": {}}},
     *     @OA\Parameter(
     *         name="id",
     *         in="path",
     *         required=true,
     *         description="Accommodation ID",
     *         @OA\Schema(type="string")
     *     ),
     *     @OA\Parameter(
     *         name="start_date",
     *         in="query",
     *         required=true,
     *         description="Start date (format: Y-m-d)",
     *         @OA\Schema(type="string", format="date")
     *     ),
     *     @OA\Parameter(
     *         name="end_date",
     *         in="query",
     *         required=true,
     *         description="End date (format: Y-m-d)",
     *         @OA\Schema(type="string", format="date")
     *     ),
     *     @OA\Parameter(
     *         name="occupancy",
     *         in="query",
     *         required=false,
     *         description="Number of occupants (defaults to minimum occupancy)",
     *         @OA\Schema(type="integer", minimum=1)
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Availability information",
     *         @OA\JsonContent(
     *             @OA\Property(property="available", type="boolean", example=true),
     *             @OA\Property(property="message", type="string", example="Accommodation is available for the specified dates"),
     *             @OA\Property(property="total_price", type="number", format="float", example=1250.00),
     *             @OA\Property(
     *                 property="pricing",
     *                 type="object",
     *                 @OA\Property(property="daily_prices", type="object", example={"2023-06-01": 250, "2023-06-02": 250, "2023-06-03": 300, "2023-06-04": 300, "2023-06-05": 150}),
     *                 @OA\Property(property="total_price", type="number", format="float", example=1250.00),
     *                 @OA\Property(property="currency", type="string", example="ZAR"),
     *                 @OA\Property(property="occupancy", type="integer", example=2),
     *                 @OA\Property(property="min_occupancy", type="integer", example=1),
     *                 @OA\Property(property="max_occupancy", type="integer", example=4)
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=400,
     *         description="Invalid input",
     *         @OA\JsonContent(
     *             @OA\Property(property="start_date", type="array", @OA\Items(type="string"), example={"The start date field is required."}),
     *             @OA\Property(property="end_date", type="array", @OA\Items(type="string"), example={"The end date must be a date after start date."})
     *         )
     *     ),
     *     @OA\Response(
     *         response=404,
     *         description="Accommodation not found",
     *         @OA\JsonContent(
     *             @OA\Property(property="message", type="string", example="Accommodation not found")
     *         )
     *     )
     * )
     *
     * Get all unavailable dates for an accommodation.
     *
     * @param string $id The ID of the accommodation
     * @return \Illuminate\Http\JsonResponse
     *
     * @OA\Get(
     *     path="/api/accommodations/{id}/unavailable-dates",
     *     summary="Get unavailable dates for an accommodation",
     *     description="Returns a list of all unavailable dates for the specified accommodation",
     *     operationId="getAccommodationUnavailableDates",
     *     tags={"Accommodations"},
     *     security={{"sanctum": {}}},
     *     @OA\Parameter(
     *         name="id",
     *         in="path",
     *         required=true,
     *         description="Accommodation ID",
     *         @OA\Schema(type="string")
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="List of unavailable dates",
     *         @OA\JsonContent(
     *             @OA\Property(property="data", type="array", @OA\Items(
     *                 @OA\Property(property="id", type="integer", example=1),
     *                 @OA\Property(property="type", type="string", example="date_range"),
     *                 @OA\Property(property="start_date", type="string", format="date", example="2023-06-01"),
     *                 @OA\Property(property="end_date", type="string", format="date", example="2023-06-05"),
     *                 @OA\Property(property="days_of_week", type="array", @OA\Items(type="integer"), example={0, 6}),
     *                 @OA\Property(property="reason", type="string", example="Maintenance"),
     *                 @OA\Property(property="active", type="boolean", example=true)
     *             ))
     *         )
     *     ),
     *     @OA\Response(
     *         response=404,
     *         description="Accommodation not found",
     *         @OA\JsonContent(
     *             @OA\Property(property="message", type="string", example="Accommodation not found or you do not have permission to access it")
     *         )
     *     ),
     *     @OA\Response(
     *         response=401,
     *         description="Unauthenticated",
     *         @OA\JsonContent(
     *             @OA\Property(property="message", type="string", example="Unauthenticated.")
     *         )
     *     )
     * )
     */
    public function getUnavailableDates(string $id, Request $request)
    {
        // Check if this is a widget request (has authenticated_site attribute)
        $isWidgetRequest = $request->attributes->has('authenticated_site');

        if ($isWidgetRequest) {
            // For widget requests, we only check published accommodations
            $accommodation = Accommodation::published()
                ->where('id', $id)
                ->first();
        } else {
            // For authenticated user requests
            $user = Auth::user();
            $teamIds = $user->allTeams()->pluck('id')->toArray();

            // Find accommodation and ensure it belongs to the authenticated user or their team and is published
            $accommodation = Accommodation::published()
                ->where('id', $id)
                ->where(function ($query) use ($user, $teamIds) {
                    $query->where('user_id', $user->id);

                    if (!empty($teamIds)) {
                        $query->orWhereIn('team_id', $teamIds);
                    }
                })
                ->first();
        }

        if (!$accommodation) {
            return response()->json(['message' => 'Accommodation not found or you do not have permission to access it'], 404);
        }

        // Get all active unavailable periods for this accommodation
        $unavailablePeriods = \App\Models\AccommodationUnavailablePeriod::where('accommodation_id', $id)
            ->where('active', true)
            ->orderBy('start_date')
            ->get();

        return response()->json([
            'data' => $unavailablePeriods
        ]);
    }

    /**
     * Store a newly created accommodation.
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     *
     * @OA\Post(
     *     path="/api/accommodations",
     *     summary="Create a new accommodation",
     *     description="Creates a new accommodation for the authenticated user",
     *     operationId="storeAccommodation",
     *     tags={"Accommodations"},
     *     security={{"sanctum": {}}},
     *     @OA\RequestBody(
     *         required=true,
     *         description="Accommodation data",
     *         @OA\JsonContent(
     *             required={"name", "description", "address", "city", "country", "price"},
     *             @OA\Property(property="name", type="string", example="Beach House"),
     *             @OA\Property(property="description", type="string", example="Beautiful beach house with ocean view"),
     *             @OA\Property(property="address", type="string", example="123 Beach Road"),
     *             @OA\Property(property="city", type="string", example="Cape Town"),
     *             @OA\Property(property="province", type="string", example="Western Cape"),
     *             @OA\Property(property="post_code", type="string", example="8001"),
     *             @OA\Property(property="country", type="string", example="South Africa"),
     *             @OA\Property(property="min_occupancy", type="integer", example=2),
     *             @OA\Property(property="max_occupancy", type="integer", example=4),
     *             @OA\Property(property="minimum_booking_notice", type="integer", example=1),
     *             @OA\Property(property="price", type="number", format="float", example=100.00),
     *             @OA\Property(property="additional_person_price", type="number", format="float", example=25.00),
     *             @OA\Property(property="accommodation_group_id", type="integer", nullable=true, example=1)
     *         )
     *     ),
     *     @OA\Response(
     *         response=201,
     *         description="Accommodation created successfully",
     *         @OA\JsonContent(
     *             @OA\Property(property="data", type="object",
     *                 @OA\Property(property="id", type="integer", example=1),
     *                 @OA\Property(property="name", type="string", example="Beach House"),
     *                 @OA\Property(property="description", type="string", example="Beautiful beach house with ocean view"),
     *                 @OA\Property(property="address", type="string", example="123 Beach Road"),
     *                 @OA\Property(property="city", type="string", example="Cape Town"),
     *                 @OA\Property(property="province", type="string", example="Western Cape"),
     *                 @OA\Property(property="post_code", type="string", example="8001"),
     *                 @OA\Property(property="country", type="string", example="South Africa"),
     *                 @OA\Property(property="min_occupancy", type="integer", example=2),
     *                 @OA\Property(property="max_occupancy", type="integer", example=4),
     *                 @OA\Property(property="minimum_booking_notice", type="integer", example=1),
     *                 @OA\Property(property="published", type="boolean", example=false)
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=422,
     *         description="Validation error",
     *         @OA\JsonContent(
     *             @OA\Property(property="message", type="string", example="The given data was invalid."),
     *             @OA\Property(property="errors", type="object",
     *                 @OA\Property(property="name", type="array", @OA\Items(type="string", example="The name field is required.")),
     *                 @OA\Property(property="description", type="array", @OA\Items(type="string", example="The description field is required."))
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=401,
     *         description="Unauthenticated",
     *         @OA\JsonContent(
     *             @OA\Property(property="message", type="string", example="Unauthenticated.")
     *         )
     *     )
     * )
     */
    public function store(Request $request)
    {
        $user = Auth::user();

        $validator = \Illuminate\Support\Facades\Validator::make($request->all(), [
            'name' => 'required|string|max:255',
            'description' => 'required|string',
            'address' => 'required|string|max:255',
            'city' => 'required|string|max:255',
            'province' => 'nullable|string|max:255',
            'post_code' => 'nullable|string|max:20',
            'country' => 'required|string|max:255',
            'min_occupancy' => 'nullable|integer|min:1',
            'max_occupancy' => 'nullable|integer|min:1|gte:min_occupancy',
            'minimum_booking_notice' => 'nullable|integer|min:0',
            'price' => 'required|numeric|min:0',
            'additional_person_price' => 'nullable|numeric|min:0',
            'accommodation_group_id' => 'nullable|integer|exists:accommodation_groups,id'
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        // Check if the accommodation group belongs to the user
        if ($request->has('accommodation_group_id') && $request->accommodation_group_id) {
            $group = \App\Models\AccommodationGroup::find($request->accommodation_group_id);
            if (!$group || $group->user_id !== $user->id) {
                return response()->json(['message' => 'The selected accommodation group does not belong to you.'], 403);
            }
        }

        // Create the accommodation
        $accommodation = new \App\Models\Accommodation([
            'name' => $request->name,
            'description' => $request->description,
            'address' => $request->address,
            'city' => $request->city,
            'province' => $request->province,
            'post_code' => $request->post_code,
            'country' => $request->country,
            'min_occupancy' => $request->min_occupancy ?? 1,
            'max_occupancy' => $request->max_occupancy ?? 2,
            'minimum_booking_notice' => $request->minimum_booking_notice ?? 0,
            'published' => false, // Default to unpublished
            'accommodation_group_id' => $request->accommodation_group_id
        ]);

        $accommodation->user_id = $user->id;
        $accommodation->team_id = $user->current_team_id;
        $accommodation->save();

        // Create default price
        \App\Models\AccommodationPrice::create([
            'accommodation_id' => $accommodation->id,
            'price' => $request->price,
            'additional_person_price' => $request->additional_person_price ?? 0,
            'type' => 'default',
            'priority' => 1
        ]);

        return response()->json(['data' => $accommodation], 201);
    }

    /**
     * Update the specified accommodation.
     *
     * @param Request $request
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     *
     * @OA\Put(
     *     path="/api/accommodations/{id}",
     *     summary="Update an accommodation",
     *     description="Updates an existing accommodation belonging to the authenticated user",
     *     operationId="updateAccommodation",
     *     tags={"Accommodations"},
     *     security={{"sanctum": {}}},
     *     @OA\Parameter(
     *         name="id",
     *         in="path",
     *         description="Accommodation ID",
     *         required=true,
     *         @OA\Schema(type="integer")
     *     ),
     *     @OA\RequestBody(
     *         required=true,
     *         description="Accommodation data",
     *         @OA\JsonContent(
     *             @OA\Property(property="name", type="string", example="Updated Beach House"),
     *             @OA\Property(property="description", type="string", example="Updated description"),
     *             @OA\Property(property="address", type="string", example="123 Beach Road"),
     *             @OA\Property(property="city", type="string", example="Cape Town"),
     *             @OA\Property(property="province", type="string", example="Western Cape"),
     *             @OA\Property(property="post_code", type="string", example="8001"),
     *             @OA\Property(property="country", type="string", example="South Africa"),
     *             @OA\Property(property="min_occupancy", type="integer", example=2),
     *             @OA\Property(property="max_occupancy", type="integer", example=4),
     *             @OA\Property(property="minimum_booking_notice", type="integer", example=1),
     *             @OA\Property(property="price", type="number", format="float", example=150.00),
     *             @OA\Property(property="additional_person_price", type="number", format="float", example=35.00),
     *             @OA\Property(property="accommodation_group_id", type="integer", nullable=true, example=1)
     *         )
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Accommodation updated successfully",
     *         @OA\JsonContent(
     *             @OA\Property(property="data", type="object",
     *                 @OA\Property(property="id", type="integer", example=1),
     *                 @OA\Property(property="name", type="string", example="Updated Beach House"),
     *                 @OA\Property(property="description", type="string", example="Updated description"),
     *                 @OA\Property(property="address", type="string", example="123 Beach Road"),
     *                 @OA\Property(property="city", type="string", example="Cape Town"),
     *                 @OA\Property(property="province", type="string", example="Western Cape"),
     *                 @OA\Property(property="post_code", type="string", example="8001"),
     *                 @OA\Property(property="country", type="string", example="South Africa"),
     *                 @OA\Property(property="min_occupancy", type="integer", example=2),
     *                 @OA\Property(property="max_occupancy", type="integer", example=4),
     *                 @OA\Property(property="minimum_booking_notice", type="integer", example=1),
     *                 @OA\Property(property="published", type="boolean", example=false)
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=404,
     *         description="Accommodation not found",
     *         @OA\JsonContent(
     *             @OA\Property(property="message", type="string", example="Accommodation not found or you do not have permission to update it")
     *         )
     *     ),
     *     @OA\Response(
     *         response=403,
     *         description="Forbidden",
     *         @OA\JsonContent(
     *             @OA\Property(property="message", type="string", example="You do not have permission to update this accommodation")
     *         )
     *     ),
     *     @OA\Response(
     *         response=422,
     *         description="Validation error",
     *         @OA\JsonContent(
     *             @OA\Property(property="message", type="string", example="The given data was invalid."),
     *             @OA\Property(property="errors", type="object",
     *                 @OA\Property(property="name", type="array", @OA\Items(type="string", example="The name field is required.")),
     *                 @OA\Property(property="description", type="array", @OA\Items(type="string", example="The description field is required."))
     *             )
     *         )
     *     )
     * )
     */
    public function update(Request $request, $id)
    {
        $user = Auth::user();
        $teamIds = $user->allTeams()->pluck('id')->toArray();

        // First check if the accommodation exists at all
        $accommodation = \App\Models\Accommodation::find($id);

        if (!$accommodation) {
            return response()->json(['message' => 'Accommodation not found'], 404);
        }

        // Then check if it belongs to the authenticated user or their team
        if ($accommodation->user_id !== $user->id && !in_array($accommodation->team_id, $teamIds)) {
            return response()->json(['message' => 'You do not have permission to update this accommodation'], 403);
        }

        $validator = \Illuminate\Support\Facades\Validator::make($request->all(), [
            'name' => 'sometimes|required|string|max:255',
            'description' => 'sometimes|required|string',
            'address' => 'sometimes|required|string|max:255',
            'city' => 'sometimes|required|string|max:255',
            'province' => 'sometimes|nullable|string|max:255',
            'post_code' => 'sometimes|nullable|string|max:20',
            'country' => 'sometimes|required|string|max:255',
            'min_occupancy' => 'sometimes|integer|min:1',
            'max_occupancy' => 'sometimes|integer|min:1|gte:min_occupancy',
            'minimum_booking_notice' => 'sometimes|integer|min:0',
            'price' => 'sometimes|required|numeric|min:0',
            'additional_person_price' => 'sometimes|nullable|numeric|min:0',
            'accommodation_group_id' => 'sometimes|nullable|integer|exists:accommodation_groups,id'
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        // Check if the accommodation group belongs to the user
        if ($request->has('accommodation_group_id') && $request->accommodation_group_id) {
            $group = \App\Models\AccommodationGroup::find($request->accommodation_group_id);
            if (!$group || $group->user_id !== $user->id) {
                return response()->json(['message' => 'The selected accommodation group does not belong to you.'], 403);
            }
        }

        // Update the accommodation
        $accommodation->fill($request->only([
            'name',
            'description',
            'address',
            'city',
            'province',
            'post_code',
            'country',
            'min_occupancy',
            'max_occupancy',
            'minimum_booking_notice',
            'accommodation_group_id'
        ]));

        $accommodation->save();

        // Update default price if provided
        if ($request->has('price')) {
            $defaultPrice = \App\Models\AccommodationPrice::firstOrNew([
                'accommodation_id' => $accommodation->id,
                'type' => 'default'
            ]);

            $defaultPrice->price = $request->price;

            if ($request->has('additional_person_price')) {
                $defaultPrice->additional_person_price = $request->additional_person_price;
            }

            $defaultPrice->priority = 1;
            $defaultPrice->save();
        }

        return response()->json(['data' => $accommodation]);
    }

    /**
     * Remove the specified accommodation.
     *
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     *
     * @OA\Delete(
     *     path="/api/accommodations/{id}",
     *     summary="Delete an accommodation",
     *     description="Deletes an existing accommodation belonging to the authenticated user",
     *     operationId="deleteAccommodation",
     *     tags={"Accommodations"},
     *     security={{"sanctum": {}}},
     *     @OA\Parameter(
     *         name="id",
     *         in="path",
     *         description="Accommodation ID",
     *         required=true,
     *         @OA\Schema(type="integer")
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Accommodation deleted successfully",
     *         @OA\JsonContent(
     *             @OA\Property(property="message", type="string", example="Accommodation deleted successfully")
     *         )
     *     ),
     *     @OA\Response(
     *         response=404,
     *         description="Accommodation not found",
     *         @OA\JsonContent(
     *             @OA\Property(property="message", type="string", example="Accommodation not found or you do not have permission to delete it")
     *         )
     *     ),
     *     @OA\Response(
     *         response=403,
     *         description="Forbidden",
     *         @OA\JsonContent(
     *             @OA\Property(property="message", type="string", example="You do not have permission to delete this accommodation")
     *         )
     *     )
     * )
     */
    public function destroy($id)
    {
        $user = Auth::user();
        $teamIds = $user->allTeams()->pluck('id')->toArray();

        // First check if the accommodation exists at all
        $accommodation = \App\Models\Accommodation::find($id);

        if (!$accommodation) {
            return response()->json(['message' => 'Accommodation not found'], 404);
        }

        // Then check if it belongs to the authenticated user or their team
        if ($accommodation->user_id !== $user->id && !in_array($accommodation->team_id, $teamIds)) {
            return response()->json(['message' => 'You do not have permission to delete this accommodation'], 403);
        }

        // Soft delete the accommodation
        $accommodation->delete();

        return response()->json(['message' => 'Accommodation deleted successfully']);
    }
}

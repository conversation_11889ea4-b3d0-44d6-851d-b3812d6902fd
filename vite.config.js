import { defineConfig } from 'vite';
import laravel from 'laravel-vite-plugin';
import vue from '@vitejs/plugin-vue';
import tailwindcss from "@tailwindcss/vite";

export default defineConfig({
    plugins: [
        laravel({
            input: 'resources/js/app.js',
            refresh: true,
        }),
        vue({
            template: {
                transformAssetUrls: {
                    base: null,
                    includeAbsolute: false,
                },
                compilerOptions: {
                    isCustomElement: tag => tag === 'availability-search'
                }
            },
        }),
        tailwindcss(),
    ],
    build: {
        // Add chunk size warning limit
        chunkSizeWarningLimit: 1000,
        rollupOptions: {
            output: {
                manualChunks: {
                    // Split Vue and related packages
                    'vue-vendor': ['vue', '@inertiajs/vue3'],
                    // Split UI related packages
                    'ui-vendor': ['preline'],
                    // Split other vendor packages
                    'vendor': ['lodash']
                }
            }
        }
    }
});

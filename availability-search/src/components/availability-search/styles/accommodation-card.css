/* Accommodation card styles */
.accommodation-card {
  background-color: white;
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.08);
  overflow: hidden;
  text-decoration: none;
  transition: all 0.3s ease;
  display: block;
  position: relative;
  height: 100%;
  border: 1px solid rgba(0, 0, 0, 0.05);
  display: flex;
  flex-direction: column;
}


.accommodation-card:before {
  content: "";
  position: absolute;
  top: -10px;
  right: -10px;
  width: 100px;
  height: 100px;
  background-image: radial-gradient(circle, rgba(0, 0, 0, 0.05) 1px, transparent 1px);
  background-size: 10px 10px;
  z-index: 0;
  opacity: 0;
  transition: opacity 0.3s ease;
  pointer-events: none;
}

.accommodation-card:hover:before {
  opacity: 1;
}

.accommodation-image {
  height: 220px;
  overflow: hidden;
  position: relative;
}

.accommodation-image-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(to bottom, rgba(0, 0, 0, 0) 60%, rgba(0, 0, 0, 0.4) 100%);
  z-index: 1;
  pointer-events: none;
}

.accommodation-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.5s ease;
}

.accommodation-rating {
  position: absolute;
  top: 12px;
  right: 12px;
  background-color: rgba(255, 255, 255, 0.9);
  color: #000;
  border-radius: 20px;
  padding: 4px 10px;
  font-size: 0.85rem;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 4px;
  z-index: 2;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.rating-star {
  color: #f59e0b; /* Amber color for star */
}

.accommodation-details {
  padding: 20px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  flex-grow: 1;
}

.accommodation-info {
  margin-bottom: 16px;
}

.accommodation-name {
  margin-top: 0;
  margin-bottom: 8px;
  color: var(--color-primary, #1e3a8a);
  font-size: 1.25rem;
  font-weight: 600;
  line-height: 1.3;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.accommodation-location {
  margin: 0 0 12px;
  color: var(--color-text-light, #6b7280);
  font-size: 0.9rem;
  display: flex;
  align-items: center;
  gap: 6px;
}

.location-icon {
  flex-shrink: 0;
  color: var(--color-text-light, #6b7280);
}

.accommodation-description {
  margin: 0;
  color: var(--color-text, #374151);
  font-size: 0.9rem;
  line-height: 1.5;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.accommodation-footer {
  padding-top: 12px;
  border-top: 1px solid rgba(0, 0, 0, 0.05);
  margin-top: auto;
  display: flex;
  flex-direction: column;
  gap: 24px;
  align-items: center;
}

@media (min-width: 768px) {
	.accommodation-footer {
		flex-direction: row;
		justify-content: space-between;
	}
}

.accommodation-footer button {
	padding: 8px 16px;
	background: var(--color-primary);
	color: white;
	border: none;
	border-radius: 4px;
	transition: opacity 0.3s ease-in-out;
}

.accommodation-footer button:hover {
	opacity: 0.8;
}

.accommodation-price {
  margin: 0;
  color: var(--color-text, #374151);
  font-size: 0.95rem;
}

.accommodation-price span {
  color: var(--color-accent-primary, #2563eb);
  font-weight: 700;
  font-size: 1.1rem;
}
